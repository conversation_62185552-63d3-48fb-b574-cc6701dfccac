#include <QtTest>
#include <QTemporaryDir>
#include <QDebug>
#include <QSignalSpy>
#include <QEventLoop>
#include <QTimer>
#include <QFileInfo>
#include "DatabaseManager.h"

/**
 * @brief 密码加密和安全存储功能专项测试
 * 
 * 测试DatabaseManager的新增密码管理功能：
 * - 随机密码生成
 * - AES-256-CBC加密/解密
 * - QtKeychain密钥存储
 * - 配置文件密码存储
 * - 完整工作流程
 */
class PasswordEncryptionTest : public QObject
{
    Q_OBJECT

public:
    PasswordEncryptionTest();
    ~PasswordEncryptionTest();

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 基础功能测试
    void testRandomPasswordGeneration();
    void testPasswordEncryptionDecryption();
    void testEncryptionKeyGeneration();
    
    // 存储功能测试
    void testKeychainStorage();
    void testConfigFileStorage();
    
    // 集成测试
    // void testAutoPasswordWorkflow();
    // void testStoredPasswordWorkflow();
    // void testPasswordChangeWorkflow();
    
    // // 错误处理测试
    // void testInvalidInputHandling();
    // void testMissingDataHandling();
    
    // // 性能测试
    // void testEncryptionPerformance();

private:
    QTemporaryDir* m_tempDir;
    QString m_testDbPath;
    
    // 辅助方法
    void waitForAsyncOperation(std::function<void(std::function<void()>)> operation, int timeoutMs = 5000);
};

PasswordEncryptionTest::PasswordEncryptionTest()
    : m_tempDir(nullptr)
{
}

PasswordEncryptionTest::~PasswordEncryptionTest()
{
}

void PasswordEncryptionTest::initTestCase()
{
    qDebug() << "=== Password Encryption Test Suite ===";
    
    // 创建临时目录
    m_tempDir = new QTemporaryDir();
    QVERIFY(m_tempDir->isValid());
    
    m_testDbPath = m_tempDir->path() + "/password_test.db";
    qDebug() << "Test database path:" << m_testDbPath;
}

void PasswordEncryptionTest::cleanupTestCase()
{
    delete m_tempDir;
    m_tempDir = nullptr;
}

void PasswordEncryptionTest::init()
{
    // 每个测试前的准备工作
}

void PasswordEncryptionTest::cleanup()
{
    // 每个测试后的清理工作
}

void PasswordEncryptionTest::testRandomPasswordGeneration()
{
    qDebug() << "\n--- Testing Random Password Generation ---";
    
    // 测试默认长度
    QString password1 = DatabaseManager::generateRandomPassword();
    QVERIFY(!password1.isEmpty());
    QCOMPARE(password1.length(), 32);
    
    // 测试自定义长度
    for (int length : {8, 16, 24, 32, 64}) {
        QString password = DatabaseManager::generateRandomPassword(length);
        QVERIFY(!password.isEmpty());
        QCOMPARE(password.length(), length);
        qDebug() << QString("Generated %1-char password: %2").arg(length).arg(password);
    }
    
    // 测试唯一性
    QSet<QString> passwords;
    for (int i = 0; i < 100; ++i) {
        QString password = DatabaseManager::generateRandomPassword(16);
        QVERIFY(!passwords.contains(password)); // 应该是唯一的
        passwords.insert(password);
    }
    
    qDebug() << "Generated 100 unique passwords successfully";
}

void PasswordEncryptionTest::testPasswordEncryptionDecryption()
{
    qDebug() << "\n--- Testing Password Encryption/Decryption ---";
    
    QStringList testPasswords = {
        "simple",
        "Complex123!@#",
        "中文密码测试",
        "Very_Long_Password_With_Many_Characters_1234567890!@#$%^&*()",
        ""  // 空密码
    };
    
    QString encryptionKey = DatabaseManager::generateRandomPassword(32);
    
    for (const QString &originalPassword : testPasswords) {
        if (originalPassword.isEmpty()) {
            // 空密码应该返回空结果
            QString encrypted = DatabaseManager::encryptPassword(originalPassword, encryptionKey);
            QVERIFY(encrypted.isEmpty());
            continue;
        }
        
        // 加密
        QString encryptedPassword = DatabaseManager::encryptPassword(originalPassword, encryptionKey);
        QVERIFY(!encryptedPassword.isEmpty());
        QVERIFY(encryptedPassword != originalPassword);
        
        // 解密
        QString decryptedPassword = DatabaseManager::decryptPassword(encryptedPassword, encryptionKey);
        QCOMPARE(decryptedPassword, originalPassword);
        
        qDebug() << QString("✓ Password: '%1' -> Encrypted: '%2' -> Decrypted: '%3'")
                    .arg(originalPassword, encryptedPassword.left(20) + "...", decryptedPassword);
    }
}

void PasswordEncryptionTest::testEncryptionKeyGeneration()
{
    qDebug() << "\n--- Testing Encryption Key Generation ---";
    
    DatabaseManager dbManager;
    
    // 通过反射或友元访问私有方法（这里我们通过公共接口测试）
    QString key1 = DatabaseManager::generateRandomPassword(32);
    QString key2 = DatabaseManager::generateRandomPassword(32);
    
    QVERIFY(!key1.isEmpty());
    QVERIFY(!key2.isEmpty());
    QCOMPARE(key1.length(), 32);
    QCOMPARE(key2.length(), 32);
    QVERIFY(key1 != key2);
    
    qDebug() << "Key 1:" << key1;
    qDebug() << "Key 2:" << key2;
}

void PasswordEncryptionTest::testKeychainStorage()
{
    qDebug() << "\n--- Testing Keychain Storage ---";
    
    QString testKeyName = QString("test_key_%1").arg(QDateTime::currentMSecsSinceEpoch());
    QString testKey = DatabaseManager::generateRandomPassword(32);
    
    // 测试存储
    bool storeSuccess = false;
    QString storeError;
    
    waitForAsyncOperation([&](std::function<void()> done) {
        DatabaseManager::storeEncryptionKey(testKeyName, testKey,
            [&storeSuccess, &storeError, done](bool success, const QString &error) {
                storeSuccess = success;
                storeError = error;
                done();
            });
    });
    
    QVERIFY2(storeSuccess, qPrintable("Store failed: " + storeError));
    
    // 测试检索
    bool retrieveSuccess = false;
    QString retrievedKey;
    QString retrieveError;
    
    waitForAsyncOperation([&](std::function<void()> done) {
        DatabaseManager::retrieveEncryptionKey(testKeyName,
            [&retrieveSuccess, &retrievedKey, &retrieveError, done]
            (bool success, const QString &key, const QString &error) {
                retrieveSuccess = success;
                retrievedKey = key;
                retrieveError = error;
                done();
            });
    });
    
    QVERIFY2(retrieveSuccess, qPrintable("Retrieve failed: " + retrieveError));
    QCOMPARE(retrievedKey, testKey);
    
    qDebug() << "✓ Keychain storage and retrieval successful";
}

void PasswordEncryptionTest::testConfigFileStorage()
{
    qDebug() << "\n--- Testing Config File Storage ---";
    
    QString testDbPath = m_tempDir->path() + "/config_test.db";
    QString testPassword = "ConfigTestPassword123!";
    QString encryptionKey = DatabaseManager::generateRandomPassword(32);
    QString encryptedPassword = DatabaseManager::encryptPassword(testPassword, encryptionKey);
    
    // 测试存储
    bool storeResult = DatabaseManager::storeEncryptedPassword(testDbPath, encryptedPassword);
    QVERIFY(storeResult);
    
    // 测试检索
    QString retrievedPassword = DatabaseManager::retrieveEncryptedPassword(testDbPath);
    QVERIFY(!retrievedPassword.isEmpty());
    QCOMPARE(retrievedPassword, encryptedPassword);
    
    // 验证解密
    QString decryptedPassword = DatabaseManager::decryptPassword(retrievedPassword, encryptionKey);
    QCOMPARE(decryptedPassword, testPassword);
    
    qDebug() << "✓ Config file storage and retrieval successful";
}

void PasswordEncryptionTest::waitForAsyncOperation(std::function<void(std::function<void()>)> operation, int timeoutMs)
{
    QEventLoop loop;
    QTimer timeoutTimer;
    
    timeoutTimer.setSingleShot(true);
    timeoutTimer.setInterval(timeoutMs);
    
    connect(&timeoutTimer, &QTimer::timeout, &loop, &QEventLoop::quit);
    
    operation([&loop]() {
        loop.quit();
    });
    
    timeoutTimer.start();
    loop.exec();
}

QTEST_MAIN(PasswordEncryptionTest)

#include "tst_password_encryption.moc"
