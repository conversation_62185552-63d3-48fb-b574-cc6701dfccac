# 数据库密码加密和安全存储

本文档介绍如何使用DatabaseManager的新功能来实现数据库密码的自动生成、加密和安全存储。

## 功能概述

新的密码管理功能包括：

1. **随机密码生成** - 使用PocoCrypto生成安全的随机密码
2. **对称加密** - 使用AES-256-CBC算法加密数据库密码
3. **安全存储** - 使用QtKeychain将加密密钥存储在系统密钥链中
4. **自动化工作流** - 在创建新数据库时自动处理整个流程

## 核心方法

### 静态方法

#### `generateRandomPassword(int length = 32)`
生成指定长度的随机密码。

```cpp
QString password = DatabaseManager::generateRandomPassword(16);
qDebug() << "生成的密码:" << password;
```

#### `encryptPassword(const QString &password, const QString &encryptionKey)`
使用AES-256-CBC算法加密密码。

```cpp
QString encryptedPassword = DatabaseManager::encryptPassword(password, encryptionKey);
```

#### `decryptPassword(const QString &encryptedPassword, const QString &encryptionKey)`
解密已加密的密码。

```cpp
QString decryptedPassword = DatabaseManager::decryptPassword(encryptedPassword, encryptionKey);
```

#### `storeEncryptionKey(const QString &keyName, const QString &key, callback)`
将加密密钥存储到系统密钥链中。

```cpp
DatabaseManager::storeEncryptionKey("my_key", encryptionKey, 
    [](bool success, const QString &error) {
        if (success) {
            qDebug() << "密钥存储成功";
        } else {
            qDebug() << "密钥存储失败:" << error;
        }
    });
```

#### `retrieveEncryptionKey(const QString &keyName, callback)`
从系统密钥链中检索加密密钥。

```cpp
DatabaseManager::retrieveEncryptionKey("my_key",
    [](bool success, const QString &key, const QString &error) {
        if (success) {
            qDebug() << "密钥检索成功:" << key;
        } else {
            qDebug() << "密钥检索失败:" << error;
        }
    });
```

### 实例方法

#### `initialize(const QString &dbPath, bool autoGeneratePassword, int maxConnections, callback)`
使用自动密码生成初始化数据库。

```cpp
DatabaseManager dbManager;
bool result = dbManager.initialize("mydb.db", true, 10,
    [](const QString &generatedPassword) {
        qDebug() << "生成的密码:" << generatedPassword;
    });
```

## 使用场景

### 场景1：新数据库创建

当创建新数据库时，使用自动密码生成：

```cpp
DatabaseManager *dbManager = new DatabaseManager();

// 自动生成密码并初始化数据库
bool result = dbManager->initialize("new_database.db", true, 10,
    [](const QString &generatedPassword) {
        qDebug() << "数据库密码已生成并安全存储";
        // 密码已自动加密并存储到系统密钥链
    });
```

### 场景2：连接现有数据库

当连接到现有的加密数据库时：

```cpp
// 1. 检索加密密钥
QString keyStoreName = "db_key_mydatabase";
DatabaseManager::retrieveEncryptionKey(keyStoreName,
    [](bool success, const QString &encryptionKey, const QString &error) {
        if (success) {
            // 2. 使用密钥解密存储的密码
            // (需要从某处获取加密的密码)
            QString decryptedPassword = DatabaseManager::decryptPassword(
                encryptedPassword, encryptionKey);
            
            // 3. 使用解密的密码连接数据库
            // (这里需要扩展DatabaseManager支持密码参数)
        }
    });
```

### 场景3：手动密码管理

如果需要手动控制密码生成和加密过程：

```cpp
// 1. 生成随机密码
QString password = DatabaseManager::generateRandomPassword(24);

// 2. 生成加密密钥
QString encryptionKey = DatabaseManager::generateRandomPassword(32);

// 3. 加密密码
QString encryptedPassword = DatabaseManager::encryptPassword(password, encryptionKey);

// 4. 存储加密密钥
DatabaseManager::storeEncryptionKey("my_app_key", encryptionKey,
    [encryptedPassword](bool success, const QString &error) {
        if (success) {
            // 5. 将加密的密码存储到配置文件或数据库中
            // (具体存储方式由应用决定)
        }
    });
```

## 安全考虑

1. **密钥分离** - 加密密钥存储在系统密钥链中，与加密的密码分离存储
2. **强加密** - 使用AES-256-CBC算法进行对称加密
3. **随机性** - 使用PocoCrypto的安全随机数生成器
4. **系统集成** - 利用操作系统的密钥链服务确保安全性

## 依赖项

确保项目正确链接以下库：

- **PocoCrypto** - 用于加密和随机数生成
- **QtKeychain** - 用于系统密钥链集成
- **OpenSSL** - PocoCrypto的底层加密库

## 注意事项

1. **异步操作** - QtKeychain操作是异步的，需要使用回调函数
2. **错误处理** - 始终检查操作结果并处理错误情况
3. **密钥管理** - 确保为不同的数据库使用不同的密钥存储名称
4. **备份策略** - 考虑密钥丢失的恢复策略

## 示例代码

完整的示例代码请参考：
- `Database/examples/PasswordEncryptionExample.cpp` - 基础功能演示
- `Database/examples/SecureDatabaseExample.cpp` - 实际应用场景演示
