#include "TestRepository.h"
#include "Test.h"
#include "DatabaseManager.h"
#include "DatabaseWorker.h"
#include <QDebug>
#include <QVariant>
#include <QMap>
#include <QMutexLocker>
#include <functional>
#include "Poco/Data/Session.h"
#include "Poco/Data/Statement.h"
#include "Poco/Data/RecordSet.h"
#include "Poco/Data/Transaction.h"
#include "Poco/Exception.h"

using namespace Poco::Data;
using namespace Poco::Data::Keywords;

TestRepository::TestRepository(DatabaseManager *dbManager, QObject *parent)
    : AsyncRepositoryBase(dbManager, "TestRepository", parent) {
}

TestRepository::~TestRepository() {
}

bool TestRepository::isValid() const {
    // 检查AsyncRepositoryBase是否可用
    return AsyncRepositoryBase::isValid();
}

void TestRepository::createTest(const Test &test, std::function<void(const AsyncResult<int> &)> callback) {
    // 使用executeAsync包装实现异步线程安全操作
    executeAsync<int>(
        [this, test]() -> int {
            auto session = getThreadSafeSession();
            if (!session) {
                qWarning() << "TestRepository: Session not available";
                return -1;
            }

            try {
                // 使用bind进行参数绑定，直接使用Test对象的getter方法
                Statement stmt(*session);
                stmt <<
                        "INSERT INTO test (name, description, value, created_at, updated_at) VALUES (?, ?, ?, datetime('now'), datetime('now'))"
                        ,
                        bind(test.getName()),
                        bind(test.getDescription()),
                        bind(test.getValue());

                stmt.execute();

                // 使用into直接绑定结果变量
                int lastId = 0;
                Statement idStmt(*session);
                idStmt << "SELECT last_insert_rowid()",
                        into(lastId);
                idStmt.execute();

                qDebug() << "TestRepository: Created test record with ID:" << lastId;
                return lastId;
            } catch (const Poco::Exception &ex) {
                qWarning() << "TestRepository: Failed to create test record:" << QString::fromStdString(
                    ex.displayText());
                return -1;
            }
        },
        callback,
        "createTest"
    );
}

void TestRepository::getTestById(int id, std::function<void(const AsyncResult<std::unique_ptr<Test> > &)> callback) {
    // 使用executeAsync包装实现异步线程安全操作
    executeAsync<std::unique_ptr<Test> >(
        [this, id]() -> std::unique_ptr<Test> {
            auto session = getThreadSafeSession();
            if (!session) {
                qWarning() << "TestRepository: Session not available";
                return nullptr;
            }

            Test test;
            try {
                Statement select(*session);
                select << "SELECT id, name, description, value, created_at, updated_at FROM test WHERE id = ?",
                        into(test.data),
                        bind(id),
                        limit(1);

                size_t rowsReturned = select.execute();

                // 检查是否真正查询到了数据
                if (rowsReturned == 0 || !test.isValid()) {
                    qDebug() << "TestRepository: No test found with ID:" << id;
                    return nullptr;
                }

                qDebug() << "Found test: " << QString::fromStdString(test.getName());
                return std::make_unique<Test>(test);
            } catch (DataException &ex) {
                qWarning() << "Error finding test by ID: " << QString::fromStdString(ex.displayText());
                return nullptr;
            }
        },
        callback,
        QString("getTestById_%1").arg(id)
    );
}

void TestRepository::getAllTests(std::function<void(const AsyncResult<QList<Test> > &)> callback) {
    // 使用executeAsync包装实现异步线程安全操作
    executeAsync<QList<Test> >(
        [this]() -> QList<Test> {
            QList<Test> tests;

            auto session = getThreadSafeSession();
            if (!session) {
                qWarning() << "TestRepository: Session not available";
                return tests;
            }

            Test test; // 用于接收查询结果的临时对象

            try {
                Statement select(*session);
                select <<
                        "SELECT id, name, description, value, created_at, updated_at FROM test ORDER BY created_at DESC"
                        ,
                        into(test.data),
                        range(0, 1); // 每次读取一行

                while (!select.done()) {
                    select.execute();
                    if (test.isValid()) {
                        tests.append(test);
                    }
                }

                qDebug() << "TestRepository: Retrieved" << tests.size() << "test records";
                return tests;
            } catch (DataException &ex) {
                qWarning() << "TestRepository: Failed to get all tests:" << QString::fromStdString(ex.displayText());
                return tests;
            }
        },
        callback,
        "getAllTests"
    );
}

void TestRepository::getTestsByName(const QString &name,
                                    std::function<void(const AsyncResult<QList<Test> > &)> callback) {
    // 使用executeAsync包装实现异步线程安全操作
    executeAsync<QList<Test> >(
        [this, name]() -> QList<Test> {
            QList<Test> tests;

            auto session = getThreadSafeSession();
            if (!session) {
                qWarning() << "TestRepository: Session not available";
                return tests;
            }

            Test test; // 用于接收查询结果的临时对象

            try {
                std::string searchPattern = "%" + name.toStdString() + "%";

                Statement select(*session);
                select <<
                        "SELECT id, name, description, value, created_at, updated_at FROM test WHERE name LIKE ? ORDER BY created_at DESC"
                        ,
                        into(test.data),
                        bind(searchPattern),
                        range(0, 1); // 每次读取一行

                while (!select.done()) {
                    select.execute();
                    if (test.isValid()) {
                        tests.append(test);
                    }
                }

                qDebug() << "TestRepository: Retrieved" << tests.size() << "test records matching name:" << name;
                return tests;
            } catch (DataException &ex) {
                qWarning() << "TestRepository: Failed to get tests by name:" <<
                        QString::fromStdString(ex.displayText());
                return tests;
            }
        },
        callback,
        QString("getTestsByName_%1").arg(name)
    );
}

void TestRepository::updateTest(int id, const QString &name, const QString &description, int value,
                                std::function<void(const AsyncResult<bool> &)> callback) {
    // 使用executeAsync包装实现异步线程安全操作
    executeAsync<bool>(
        [this, id, name, description, value]() -> bool {
            auto session = getThreadSafeSession();
            if (!session) {
                qWarning() << "TestRepository: Session not available";
                return false;
            }

            try {
                std::string nameStr = name.toStdString();
                std::string descStr = description.toStdString();

                Statement stmt(*session);
                stmt <<
                        "UPDATE test SET name = ?, description = ?, value = ?, updated_at = datetime('now') WHERE id = ?"
                        ,
                        bind(nameStr),
                        bind(descStr),
                        bind(value),
                        bind(id);

                size_t affected = stmt.execute();

                if (affected > 0) {
                    qDebug() << "TestRepository: Updated test record with ID:" << id;
                    return true;
                }
                qWarning() << "TestRepository: No test record found with ID:" << id;
                return false;
            } catch (const Poco::Exception &ex) {
                qWarning() << "TestRepository: Failed to update test record:" << QString::fromStdString(
                    ex.displayText());
                return false;
            }
        },
        callback,
        QString("updateTest_%1").arg(id)
    );
}

void TestRepository::deleteTest(int id, std::function<void(const AsyncResult<bool> &)> callback) {
    // 使用executeAsync包装实现异步线程安全操作
    executeAsync<bool>(
        [this, id]() -> bool {
            auto session = getThreadSafeSession();
            if (!session) {
                qWarning() << "TestRepository: Session not available";
                return false;
            }

            try {
                Statement stmt(*session);
                stmt << "DELETE FROM test WHERE id = ?",
                        bind(id);

                size_t affected = stmt.execute();

                if (affected > 0) {
                    qDebug() << "TestRepository: Deleted test record with ID:" << id;
                    return true;
                }
                qWarning() << "TestRepository: No test record found with ID:" << id;
                return false;
            } catch (const Poco::Exception &ex) {
                qWarning() << "TestRepository: Failed to delete test record:" << QString::fromStdString(
                    ex.displayText());
                return false;
            }
        },
        callback,
        QString("deleteTest_%1").arg(id)
    );
}

void TestRepository::deleteAllTests(std::function<void(const AsyncResult<bool> &)> callback) {
    // 使用executeAsync包装实现异步线程安全操作
    executeAsync<bool>(
        [this]() -> bool {
            auto session = getThreadSafeSession();
            if (!session) {
                qWarning() << "TestRepository: Session not available";
                return false;
            }

            try {
                Statement stmt(*session);
                stmt << "DELETE FROM test";

                size_t affected = stmt.execute();

                qDebug() << "TestRepository: Deleted" << affected << "test records";
                return true;
            } catch (const Poco::Exception &ex) {
                qWarning() << "TestRepository: Failed to delete all test records:" << QString::fromStdString(
                    ex.displayText());
                return false;
            }
        },
        callback,
        "deleteAllTests"
    );
}

void TestRepository::getTestCount(std::function<void(const AsyncResult<int> &)> callback) {
    // 使用executeAsync包装实现异步线程安全操作
    executeAsync<int>(
        [this]() -> int {
            auto session = getThreadSafeSession();
            if (!session) {
                qWarning() << "TestRepository: Session not available";
                return 0;
            }

            try {
                int count = 0;
                Statement stmt(*session);
                stmt << "SELECT COUNT(*) FROM test",
                        into(count);
                stmt.execute();

                return count;
            } catch (const Poco::Exception &ex) {
                qWarning() << "TestRepository: Failed to get test count:" << QString::fromStdString(ex.displayText());
                return 0;
            }
        },
        callback,
        "getTestCount"
    );
}

// ==================== 高级ORM特性实现 ====================

void TestRepository::batchCreateTests(const QList<Test> &tests,
                                      std::function<void(const AsyncResult<int> &)> callback) {
    // 使用executeAsync包装实现异步线程安全操作
    executeAsync<int>(
        [this, tests]() -> int {
            auto session = getThreadSafeSession();
            if (!session || tests.isEmpty()) {
                qWarning() << "TestRepository: Session not available or empty test list";
                return 0;
            }

            int successCount = 0;

            auto operation = [&]() -> bool {
                try {
                    for (const Test &test: tests) {
                        Statement stmt(*session);
                        stmt <<
                                "INSERT INTO test (name, description, value, created_at, updated_at) VALUES (?, ?, ?, datetime('now'), datetime('now'))"
                                ,
                                bind(test.getName()),
                                bind(test.getDescription()),
                                bind(test.getValue());

                        size_t affected = stmt.execute();
                        if (affected > 0) {
                            successCount++;
                        }
                    }
                    return true;
                } catch (const Poco::Exception &ex) {
                    qWarning() << "TestRepository: Batch create failed:" << QString::fromStdString(ex.displayText());
                    return false;
                }
            };

            // 使用事务执行批量操作
            try {
                Transaction transaction(*session);
                if (operation()) {
                    transaction.commit();
                    qDebug() << "TestRepository: Batch created" << successCount << "test records";
                    return successCount;
                }
                transaction.rollback();
                qWarning() << "TestRepository: Batch create transaction failed";
                return 0;
            } catch (const Poco::Exception &ex) {
                qWarning() << "TestRepository: Transaction failed:" << QString::fromStdString(ex.displayText());
                return 0;
            }
        },
        callback,
        QString("batchCreateTests_%1").arg(tests.size())
    );
}


void TestRepository::getTestsPaginated(int offset, int limit,
                                       std::function<void(const AsyncResult<QList<Test> > &)> callback) {
    // 使用executeAsync包装实现异步线程安全操作
    executeAsync<QList<Test> >(
        [this, offset, limit]() -> QList<Test> {
            QList<Test> tests;

            auto session = getThreadSafeSession();
            if (!session || limit <= 0) {
                qWarning() << "TestRepository: Session not available or invalid limit";
                return tests;
            }

            Test test; // 用于接收查询结果的临时对象

            try {
                Statement select(*session);
                select <<
                        "SELECT id, name, description, value, created_at, updated_at FROM test ORDER BY created_at DESC LIMIT ? OFFSET ?"
                        ,
                        into(test.data),
                        bind(limit),
                        bind(offset),
                        range(0, 1); // 每次读取一行

                while (!select.done()) {
                    select.execute();
                    if (test.isValid()) {
                        tests.append(test);
                    }
                }

                qDebug() << "TestRepository: Retrieved" << tests.size() << "test records (offset:" << offset <<
                        ", limit:" <<
                        limit << ")";
                return tests;
            } catch (DataException &ex) {
                qWarning() << "TestRepository: Failed to get paginated tests:" << QString::fromStdString(
                    ex.displayText());
                return tests;
            }
        },
        callback,
        QString("getTestsPaginated_%1_%2").arg(offset).arg(limit)
    );
}

void TestRepository::batchUpdateTests(const QMap<int, Test> &updates,
                                      std::function<void(const AsyncResult<int> &)> callback) {
    // 使用executeAsync包装实现异步线程安全操作
    executeAsync<int>(
        [this, updates]() -> int {
            auto session = getThreadSafeSession();
            if (!session || updates.isEmpty()) {
                qWarning() << "TestRepository: Session not available or empty updates map";
                return 0;
            }

            int successCount = 0;

            auto operation = [&]() -> bool {
                try {
                    for (auto it = updates.begin(); it != updates.end(); ++it) {
                        int id = it.key();
                        const Test &test = it.value();

                        Statement stmt(*session);
                        stmt <<
                                "UPDATE test SET name = ?, description = ?, value = ?, updated_at = datetime('now') WHERE id = ?"
                                ,
                                bind(test.getName()),
                                bind(test.getDescription()),
                                bind(test.getValue()),
                                bind(id);

                        size_t affected = stmt.execute();
                        if (affected > 0) {
                            successCount++;
                        }
                    }
                    return true;
                } catch (const Poco::Exception &ex) {
                    qWarning() << "TestRepository: Batch update failed:" << QString::fromStdString(ex.displayText());
                    return false;
                }
            };

            // 使用事务执行批量操作
            try {
                Transaction transaction(*session);
                if (operation()) {
                    transaction.commit();
                    qDebug() << "TestRepository: Batch updated" << successCount << "test records";
                    return successCount;
                }
                transaction.rollback();
                qWarning() << "TestRepository: Batch update transaction failed";
                return 0;
            } catch (const Poco::Exception &ex) {
                qWarning() << "TestRepository: Transaction failed:" << QString::fromStdString(ex.displayText());
                return 0;
            }
        },
        callback,
        QString("batchUpdateTests_%1").arg(updates.size())
    );
}
