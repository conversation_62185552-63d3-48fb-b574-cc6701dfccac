qt_add_library(Database STATIC
        Database.qrc
        DatabaseManager.h
        DatabaseManager.cpp
        SqlFileReader.h
        SqlFileReader.cpp
        TestRepository.h
        TestRepository.cpp
        DatabaseWorker.h
        DatabaseWorker.cpp
        AsyncRepositoryBase.h
        AsyncRepositoryBase.cpp
)

target_link_libraries(Database
        PRIVATE
        Entity
        PUBLIC
        Qt6::Core
        SQLCipher          # SQLCipher 必须在 PocoData 之前链接
        PocoData
        PocoCrypto
        QtKeychain
)

if(DEVELOPMENT)
    # 让条件编译的代码高亮
    target_compile_definitions(Database
            PRIVATE
            DEVELOPMENT
    )
endif()

# 暴露 Database 的头文件目录
target_include_directories(Database
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
)

add_subdirectory(test)
